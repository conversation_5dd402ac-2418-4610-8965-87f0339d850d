using OpenQA.Selenium;
using OpenQA.Selenium.Appium;
using OpenQA.Selenium.Support.UI;
using System;
using DrMuscle.UITests.Helpers;

namespace DrMuscle.UITests.Pages
{
    public class LoginPage
    {
        private readonly AppiumDriver _driver;
        private readonly WebDriverWait _wait;
        
        // Element locators
        private AppiumElement? EmailField => FindElement(MobileBy.AccessibilityId("EmailEntry")) 
            ?? FindElement(By.XPath("//XCUIElementTypeTextField[contains(@value,'email')]"));
            
        private AppiumElement? PasswordField => FindElement(MobileBy.AccessibilityId("PasswordEntry"))
            ?? FindElement(By.XPath("//XCUIElementTypeSecureTextField[contains(@value,'password')]"));
            
        private AppiumElement? LoginButton => FindElement(MobileBy.AccessibilityId("LoginButton"))
            ?? FindElement(By.XPath("//XCUIElementTypeStaticText[@name='Log in']"));
            
        private AppiumElement? CreateAccountButton => FindElement(MobileBy.AccessibilityId("GetStartedButton"))
            ?? FindElement(By.XPath("//XCUIElementTypeStaticText[@name='Create new account']"));
            
        private AppiumElement? GoogleLoginButton => FindElement(MobileBy.AccessibilityId("LoginWithGoogleButton"));
        
        private AppiumElement? AppleLoginButton => FindElement(MobileBy.AccessibilityId("LoginWithAppleButton"));
        
        public LoginPage(AppiumDriver driver)
        {
            _driver = driver;
            _wait = new WebDriverWait(_driver, TestConfiguration.DefaultTimeout);
            Console.WriteLine($"LoginPage initialized with timeout: {TestConfiguration.DefaultTimeout.TotalSeconds}s");
        }
        
        public void Login(string email, string password)
        {
            EnterEmail(email);
            EnterPassword(password);
            TapLogin();
        }
        
        public void EnterEmail(string email)
        {
            var field = WaitForElement(() => EmailField, "Email field");
            field.Clear();
            field.SendKeys(email);
        }

        public void EnterPassword(string password)
        {
            var field = WaitForElement(() => PasswordField, "Password field");
            field.Clear();
            field.SendKeys(password);
        }

        public void TapLogin()
        {
            var button = WaitForElement(() => LoginButton, "Login button");
            button.Click();
        }

        private AppiumElement WaitForElement(Func<AppiumElement?> elementGetter, string elementName)
        {
            try
            {
                return _wait.Until(d =>
                {
                    try
                    {
                        var element = elementGetter();
                        if (element != null)
                        {
                            // Check if element is displayed and enabled
                            if (element.Displayed && element.Enabled)
                            {
                                // Additional check: ensure element has valid dimensions
                                var location = element.Location;
                                var size = element.Size;
                                if (size.Width > 0 && size.Height > 0)
                                {
                                    Console.WriteLine($"✅ Found {elementName} at ({location.X}, {location.Y}) with size {size.Width}x{size.Height}");
                                    return element;
                                }
                                else
                                {
                                    Console.WriteLine($"⚠️ {elementName} found but has invalid size: {size.Width}x{size.Height}");
                                }
                            }
                            else
                            {
                                Console.WriteLine($"⚠️ {elementName} found but not displayed ({element.Displayed}) or enabled ({element.Enabled})");
                            }
                        }
                        else
                        {
                            Console.WriteLine($"⚠️ {elementName} not found by element getter");
                        }
                        return null;
                    }
                    catch (StaleElementReferenceException)
                    {
                        Console.WriteLine($"⚠️ {elementName} became stale, retrying...");
                        return null; // Continue waiting if element becomes stale
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"⚠️ Error getting {elementName}: {ex.Message}");
                        return null; // Continue waiting if any exception occurs
                    }
                })!; // Null-forgiving operator: WebDriverWait throws exception if condition never met
            }
            catch (WebDriverTimeoutException ex)
            {
                var availableElements = GetAvailableElements();
                throw new WebDriverTimeoutException(
                    $"{elementName} not found or not ready within {TestConfiguration.DefaultTimeout.TotalSeconds}s. " +
                    $"Available elements: {availableElements}", ex);
            }
        }
        
        public void TapCreateAccount()
        {
            CreateAccountButton?.Click();
        }
        
        public void TapGoogleLogin()
        {
            GoogleLoginButton?.Click();
        }
        
        public void TapAppleLogin()
        {
            AppleLoginButton?.Click();
        }
        
        public bool IsDisplayed()
        {
            try
            {
                return EmailField != null && EmailField.Displayed;
            }
            catch
            {
                return false;
            }
        }
        
        public void WaitForPageToLoad()
        {
            try
            {
                // Enhanced waiting with onboarding handling and multiple fallback strategies
                _wait.Until(d =>
                {
                    try
                    {
                        // Strategy 0: Check if we're on onboarding and skip it
                        if (TrySkipOnboarding(d))
                        {
                            Console.WriteLine("✅ Skipped onboarding screens");
                            // Give time for navigation to complete
                            System.Threading.Thread.Sleep(2000);
                        }

                        // Strategy 1: Look for email field
                        var emailField = EmailField;
                        if (emailField != null && emailField.Displayed && emailField.Enabled)
                        {
                            Console.WriteLine("✅ Found email field - login page loaded");
                            return true;
                        }

                        // Strategy 2: Look for any text input fields
                        var textFields = d.FindElements(By.XPath("//XCUIElementTypeTextField | //XCUIElementTypeSecureTextField"));
                        var visibleTextFields = textFields.Where(e =>
                        {
                            try { return e.Displayed && e.Enabled; }
                            catch { return false; }
                        }).ToList();

                        if (visibleTextFields.Count >= 2) // Expect email and password fields
                        {
                            Console.WriteLine($"✅ Found {visibleTextFields.Count} text fields - login page likely loaded");
                            return true;
                        }

                        // Strategy 3: Look for login-related buttons or text
                        var loginElements = d.FindElements(By.XPath("//*[contains(@name,'login') or contains(@name,'Login') or contains(@name,'sign') or contains(@name,'Sign')]"));
                        var visibleLoginElements = loginElements.Where(e =>
                        {
                            try { return e.Displayed; }
                            catch { return false; }
                        }).ToList();

                        if (visibleLoginElements.Count > 0)
                        {
                            Console.WriteLine($"✅ Found {visibleLoginElements.Count} login-related elements");
                            return true;
                        }

                        // Strategy 4: Check if we have any interactive elements at all
                        var interactiveElements = d.FindElements(By.XPath("//*[@enabled='true']"));
                        Console.WriteLine($"Found {interactiveElements.Count} interactive elements, {textFields.Count} text fields");

                        return interactiveElements.Count > 3; // Require some minimum UI
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Error during page load check: {ex.Message}");
                        return false; // Continue waiting if any exception occurs
                    }
                });
            }
            catch (WebDriverTimeoutException ex)
            {
                // Provide better error context
                var availableElements = GetAvailableElements();
                throw new WebDriverTimeoutException(
                    $"Login page failed to load within {TestConfiguration.DefaultTimeout.TotalSeconds}s. " +
                    $"Available elements: {availableElements}", ex);
            }
        }

        private bool TrySkipOnboarding(AppiumDriver driver)
        {
            try
            {
                // Look for common onboarding elements and try to skip them
                var onboardingElements = new[]
                {
                    "GetStartedButton",
                    "SkipButton",
                    "NextButton",
                    "ContinueButton"
                };

                foreach (var elementId in onboardingElements)
                {
                    try
                    {
                        var element = driver.FindElement(MobileBy.AccessibilityId(elementId));
                        if (element != null && element.Displayed && element.Enabled)
                        {
                            Console.WriteLine($"🔄 Found onboarding element: {elementId}, tapping to skip");
                            element.Click();
                            System.Threading.Thread.Sleep(1000); // Wait for navigation
                            return true;
                        }
                    }
                    catch (NoSuchElementException)
                    {
                        // Element not found, continue to next
                        continue;
                    }
                }

                // Also try to find elements by text content
                var skipTexts = new[] { "Skip", "Get Started", "Continue", "Next" };
                foreach (var text in skipTexts)
                {
                    try
                    {
                        var elements = driver.FindElements(By.XPath($"//*[contains(@name,'{text}') or contains(@label,'{text}')]"));
                        var clickableElement = elements.FirstOrDefault(e =>
                        {
                            try { return e.Displayed && e.Enabled; }
                            catch { return false; }
                        });

                        if (clickableElement != null)
                        {
                            Console.WriteLine($"🔄 Found onboarding element with text '{text}', tapping to skip");
                            clickableElement.Click();
                            System.Threading.Thread.Sleep(1000); // Wait for navigation
                            return true;
                        }
                    }
                    catch (Exception)
                    {
                        // Continue to next text
                        continue;
                    }
                }

                return false; // No onboarding elements found
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error trying to skip onboarding: {ex.Message}");
                return false;
            }
        }

        private string GetAvailableElements()
        {
            try
            {
                if (_driver == null)
                    return "driver is null";

                var elements = _driver.FindElements(By.XPath("//*[@name or @value or @label]"));
                return string.Join(", ", elements.Take(10).Select(e =>
                    e.GetAttribute("name") ?? e.GetAttribute("value") ?? e.GetAttribute("label") ?? "unknown"));
            }
            catch
            {
                return "unable to enumerate";
            }
        }

        private AppiumElement? FindElement(By by)
        {
            try
            {
                return _driver.FindElement(by) as AppiumElement;
            }
            catch (NoSuchElementException)
            {
                return null;
            }
        }
    }
}